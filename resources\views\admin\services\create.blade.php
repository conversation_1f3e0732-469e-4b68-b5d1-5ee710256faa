@extends('layouts.admin')

@section('title', 'เพิ่มบริการใหม่ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-plus me-2"></i>เพิ่มบริการใหม่
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.services.index') }}">จัดการบริการ</a></li>
                        <li class="breadcrumb-item active">เพิ่มบริการใหม่</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">ข้อมูลบริการ</h3>
                        </div>
                        <form action="{{ route('admin.services.store') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="card-body">
                                @if($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="form-group">
                                    <label for="title">หัวข้อบริการ <span class="text-danger">*</span></label>
                                    <input type="text"
                                           name="title"
                                           id="title"
                                           class="form-control @error('title') is-invalid @enderror"
                                           value="{{ old('title') }}"
                                           placeholder="เช่น บริการจัดงานศพแบบครบวงจร"
                                           required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="description">รายละเอียดบริการ <span class="text-danger">*</span></label>
                                    <textarea name="description"
                                              id="description"
                                              class="form-control @error('description') is-invalid @enderror"
                                              rows="5"
                                              placeholder="อธิบายรายละเอียดของบริการ..."
                                              required>{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="price">ราคา (บาท) <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number"
                                               name="price"
                                               id="price"
                                               class="form-control @error('price') is-invalid @enderror"
                                               value="{{ old('price') }}"
                                               min="0"
                                               step="1"
                                               placeholder="0"
                                               required>
                                        <div class="input-group-append">
                                            <span class="input-group-text">บาท</span>
                                        </div>
                                        @error('price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="image">รูปภาพประกอบ</label>
                                    <div class="custom-file">
                                        <input type="file"
                                               name="image"
                                               id="image"
                                               class="custom-file-input @error('image') is-invalid @enderror"
                                               accept="image/*">
                                        <label class="custom-file-label" for="image">เลือกรูปภาพ...</label>
                                        @error('image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกบริการ
                                </button>
                                <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">คำแนะนำ</h3>
                        </div>
                        <div class="card-body">
                            <h6><i class="fas fa-lightbulb text-warning"></i> เคล็ดลับการเพิ่มบริการ</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> ใช้หัวข้อที่ชัดเจนและน่าสนใจ</li>
                                <li><i class="fas fa-check text-success"></i> อธิบายรายละเอียดให้ครบถ้วน</li>
                                <li><i class="fas fa-check text-success"></i> ใส่ราคาที่เหมาะสม</li>
                                <li><i class="fas fa-check text-success"></i> เลือกรูปภาพที่มีคุณภาพ</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Custom file input label update
document.querySelector('.custom-file-input').addEventListener('change', function(e) {
    var fileName = e.target.files[0].name;
    var nextSibling = e.target.nextElementSibling;
    nextSibling.innerText = fileName;
});
</script>
@endsection